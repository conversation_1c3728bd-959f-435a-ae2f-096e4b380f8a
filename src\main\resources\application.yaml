# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080
  port: 8077
  servlet:
    # 应用的访问路径
    context-path: /
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # 连接数满后的排队数，默认为100
    accept-count: 1000
    threads:
      # tomcat最大线程数，默认为200
      max: 800
      # Tomcat启动初始化的线程数，默认值10
      min-spare: 100
spring:
  datasource:
    username: root
    password: 123456
    url: *******************************************************************************
    driver-class-name: com.mysql.cj.jdbc.Driver
  flowable:
    url: ***************************************************************************************************
    username: root
    password: 123456
    driver-class-name: com.mysql.cj.jdbc.Driver


flowable:
  async-executor-activate: false
  database-schema-update: true