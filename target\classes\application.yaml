# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080
  port: 8077
  servlet:
    # 应用的访问路径
    context-path: /
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # 连接数满后的排队数，默认为100
    accept-count: 1000
    threads:
      # tomcat最大线程数，默认为200
      max: 800
      # Tomcat启动初始化的线程数，默认值10
      min-spare: 100
spring:
  datasource:
    # 主数据源（业务数据库）
    primary:
      jdbc-url: *************************************************************************************************
      username: root
      password: 123456
      driver-class-name: com.mysql.cj.jdbc.Driver
      type: com.alibaba.druid.pool.DruidDataSource
      initial-size: 5
      min-idle: 5
      max-active: 20
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1 FROM DUAL
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
    # Flowable工作流数据源
    flowable:
      jdbc-url: ***************************************************************************************************
      username: root
      password: 123456
      driver-class-name: com.mysql.cj.jdbc.Driver
      type: com.alibaba.druid.pool.DruidDataSource
      initial-size: 5
      min-idle: 5
      max-active: 20
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1 FROM DUAL
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false


# Flowable配置
flowable:
  # 数据库配置
  database-schema-update: create-drop  # 强制重新创建表结构
  async-executor-activate: false       # 关闭异步执行器
  # 历史记录级别
  history-level: full
  # 检查流程定义
  check-process-definitions: false