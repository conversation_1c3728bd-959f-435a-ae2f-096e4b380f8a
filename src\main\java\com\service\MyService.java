package com.service;

import org.flowable.engine.RepositoryService;
import org.flowable.engine.RuntimeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.sql.DataSource;
import java.util.Map;

/**
 * @desc: 多数据源测试服务类
 * @author: liming
 * @version: 1.0
 * @since: 2025/09/24 11:28
 */
@Service
public class MyService {
    @Autowired
    private RuntimeService runtimeService;
    @Autowired
    private RepositoryService repositoryService;
    @Autowired
    private UserService userService;

    private final JdbcTemplate flowableJdbcTemplate;

    @Autowired
    public MyService(@Qualifier("flowableDataSource") DataSource flowableDataSource) {
        this.flowableJdbcTemplate = new JdbcTemplate(flowableDataSource);
    }

    @PostConstruct
    public void init() {
        System.out.println("=== 多数据源初始化测试 ===");
        testMultiDataSource();
    }

    /**
     * 测试 Flowable 数据源连接
     */
    public String testFlowableDataSource() {
        try {
            String sql = "SELECT COUNT(*) as count FROM ACT_GE_PROPERTY";
            Map<String, Object> result = flowableJdbcTemplate.queryForMap(sql);
            return "Flowable数据源连接成功！ACT_GE_PROPERTY表中有 " + result.get("count") + " 条记录";
        } catch (Exception e) {
            return "Flowable数据源连接失败：" + e.getMessage();
        }
    }

    /**
     * 综合测试多数据源
     */
    public void testMultiDataSource() {
        System.out.println("=== 主数据源测试 ===");
        String primaryResult = userService.testPrimaryDataSource();
        System.out.println(primaryResult);

        System.out.println("\n=== Flowable数据源测试 ===");
        String flowableResult = testFlowableDataSource();
        System.out.println(flowableResult);

        System.out.println("\n=== Flowable服务测试 ===");
        System.out.println("RuntimeService: " + (runtimeService != null ? "已注入" : "未注入"));
        System.out.println("RepositoryService: " + (repositoryService != null ? "已注入" : "未注入"));
    }
}
