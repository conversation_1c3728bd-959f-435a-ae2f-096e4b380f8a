package com.service;

import org.flowable.engine.RepositoryService;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.repository.ProcessDefinition;
import org.flowable.engine.runtime.ProcessInstance;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.PostConstruct;
import java.util.List;

/**
 * @desc:
 * @author: liming
 * @version: 1.0
 * @since: 2025/09/24 11:28
 */
@Service
public class MyService {
    @Autowired
    private RuntimeService runtimeService;
    @Autowired
    private RepositoryService repositoryService;

    @PostConstruct
    public String demo() {
        listDefinition();
        ProcessInstance demo1 = runtimeService.startProcessInstanceByKey("demo1");
        return "";
    }

    public List<ProcessDefinition> listDefinition() {
        List<ProcessDefinition> processDefinitions = repositoryService.createProcessDefinitionQuery()
                .latestVersion() // 可选：只查最新版本
                .list();

        for (ProcessDefinition pd : processDefinitions) {
            System.out.println("流程定义ID: " + pd.getId());
            System.out.println("流程Key: " + pd.getKey());
            System.out.println("名称: " + pd.getName());
            System.out.println("版本: " + pd.getVersion());
            System.out.println("部署ID: " + pd.getDeploymentId());
            System.out.println("是否挂起: " + pd.isSuspended());
            System.out.println("---------------------------");
        }
        return processDefinitions;
    }
}
