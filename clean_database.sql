-- 多数据源数据库清理和创建脚本
-- 请在 MySQL 客户端中执行此脚本

-- 1. 删除并重新创建业务数据库
DROP DATABASE IF EXISTS business_db;
CREATE DATABASE business_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 2. 删除并重新创建工作流数据库（重要：清理旧的 Flowable 表）
DROP DATABASE IF EXISTS flowable_demo;
CREATE DATABASE flowable_demo CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 3. 使用业务数据库
USE business_db;

-- 4. 创建示例业务表
CREATE TABLE IF NOT EXISTS user_info (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(100),
    phone VARCHAR(20),
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户信息表';

-- 5. 插入示例数据
INSERT INTO user_info (username, email, phone) VALUES
('admin', '<EMAIL>', '***********'),
('user1', '<EMAIL>', '***********'),
('user2', '<EMAIL>', '***********');

-- 6. 验证数据
SELECT '业务数据库创建完成' AS message;
SELECT COUNT(*) AS user_count FROM user_info;

-- 7. 切换到工作流数据库验证
USE flowable_demo;
SELECT '工作流数据库创建完成，表数量:' AS message, COUNT(*) AS table_count
FROM information_schema.tables
WHERE table_schema = 'flowable_demo';
