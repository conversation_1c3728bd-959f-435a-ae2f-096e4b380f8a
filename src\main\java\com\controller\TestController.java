package com.controller;

import com.service.MyService;
import com.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 多数据源测试控制器
 */
@RestController
@RequestMapping("/test")
public class TestController {

    @Autowired
    private MyService myService;

    @Autowired
    private UserService userService;

    /**
     * 测试多数据源连接
     */
    @GetMapping("/datasource")
    public Map<String, Object> testDataSource() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 测试主数据源
            String primaryResult = userService.testPrimaryDataSource();
            result.put("primaryDataSource", primaryResult);
            
            // 测试 Flowable 数据源
            String flowableResult = myService.testFlowableDataSource();
            result.put("flowableDataSource", flowableResult);
            
            result.put("status", "success");
            result.put("message", "多数据源测试完成");
            
        } catch (Exception e) {
            result.put("status", "error");
            result.put("message", "测试失败：" + e.getMessage());
        }
        
        return result;
    }

    /**
     * 获取用户列表
     */
    @GetMapping("/users")
    public Map<String, Object> getUsers() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            List<Map<String, Object>> users = userService.getAllUsers();
            result.put("status", "success");
            result.put("data", users);
            result.put("count", users.size());
        } catch (Exception e) {
            result.put("status", "error");
            result.put("message", "获取用户列表失败：" + e.getMessage());
        }
        
        return result;
    }

    /**
     * 健康检查
     */
    @GetMapping("/health")
    public Map<String, Object> health() {
        Map<String, Object> result = new HashMap<>();
        result.put("status", "ok");
        result.put("message", "多数据源应用运行正常");
        result.put("timestamp", System.currentTimeMillis());
        return result;
    }
}
