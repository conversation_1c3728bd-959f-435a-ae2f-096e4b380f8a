<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="986bc941-837d-4403-af54-9c3beb944d5b" name="Default Changelist" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="mavenHome" value="D:\local\idea2020\plugins\maven\lib\maven3" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectId" id="337mttYoC0whknsGixdJHmwFkQD" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">
    <property name="RequestMappingsPanelOrder0" value="0" />
    <property name="RequestMappingsPanelOrder1" value="1" />
    <property name="RequestMappingsPanelWidth0" value="75" />
    <property name="RequestMappingsPanelWidth1" value="75" />
    <property name="RunOnceActivity.OpenProjectViewOnStart" value="true" />
    <property name="aspect.path.notification.shown" value="true" />
  </component>
  <component name="RunManager">
    <configuration default="true" type="ArquillianTestNG" factoryName="" nameIsGenerated="true">
      <option name="arquillianRunConfiguration">
        <value>
          <option name="containerStateName" value="" />
        </value>
      </option>
      <option name="TEST_OBJECT" value="CLASS" />
      <properties />
      <listeners />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="Application" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" temporary="true" nameIsGenerated="true">
      <module name="flowable" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.Application" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Spring Boot.Application" />
      </list>
    </recent_temporary>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="986bc941-837d-4403-af54-9c3beb944d5b" name="Default Changelist" comment="" />
      <created>1758676704437</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1758676704437</updated>
      <workItem from="1758676705493" duration="1715000" />
      <workItem from="1758678477688" duration="7207000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="WindowStateProjectService">
    <state width="425" height="466" key="DebuggerActiveHint" timestamp="1758691035885">
      <screen x="0" y="0" width="1536" height="816" />
    </state>
    <state width="425" height="466" key="DebuggerActiveHint/0.0.1536.816/-312.-1440.2560.1392@0.0.1536.816" timestamp="1758691035885" />
    <state width="1493" height="69" key="GridCell.Tab.0.bottom" timestamp="1758691860387">
      <screen x="0" y="0" width="1536" height="816" />
    </state>
    <state width="1493" height="69" key="GridCell.Tab.0.bottom/0.0.1536.816/-312.-1440.2560.1392@0.0.1536.816" timestamp="1758691860387" />
    <state width="1493" height="69" key="GridCell.Tab.0.center" timestamp="1758691860387">
      <screen x="0" y="0" width="1536" height="816" />
    </state>
    <state width="1493" height="69" key="GridCell.Tab.0.center/0.0.1536.816/-312.-1440.2560.1392@0.0.1536.816" timestamp="1758691860387" />
    <state width="1493" height="69" key="GridCell.Tab.0.left" timestamp="1758691860387">
      <screen x="0" y="0" width="1536" height="816" />
    </state>
    <state width="1493" height="69" key="GridCell.Tab.0.left/0.0.1536.816/-312.-1440.2560.1392@0.0.1536.816" timestamp="1758691860387" />
    <state width="1493" height="69" key="GridCell.Tab.0.right" timestamp="1758691860387">
      <screen x="0" y="0" width="1536" height="816" />
    </state>
    <state width="1493" height="69" key="GridCell.Tab.0.right/0.0.1536.816/-312.-1440.2560.1392@0.0.1536.816" timestamp="1758691860387" />
    <state width="1493" height="69" key="GridCell.Tab.1.bottom" timestamp="1758691860273">
      <screen x="0" y="0" width="1536" height="816" />
    </state>
    <state width="1493" height="69" key="GridCell.Tab.1.bottom/0.0.1536.816/-312.-1440.2560.1392@0.0.1536.816" timestamp="1758691860273" />
    <state width="1493" height="69" key="GridCell.Tab.1.center" timestamp="1758691860273">
      <screen x="0" y="0" width="1536" height="816" />
    </state>
    <state width="1493" height="69" key="GridCell.Tab.1.center/0.0.1536.816/-312.-1440.2560.1392@0.0.1536.816" timestamp="1758691860273" />
    <state width="1493" height="69" key="GridCell.Tab.1.left" timestamp="1758691860273">
      <screen x="0" y="0" width="1536" height="816" />
    </state>
    <state width="1493" height="69" key="GridCell.Tab.1.left/0.0.1536.816/-312.-1440.2560.1392@0.0.1536.816" timestamp="1758691860273" />
    <state width="1493" height="69" key="GridCell.Tab.1.right" timestamp="1758691860273">
      <screen x="0" y="0" width="1536" height="816" />
    </state>
    <state width="1493" height="69" key="GridCell.Tab.1.right/0.0.1536.816/-312.-1440.2560.1392@0.0.1536.816" timestamp="1758691860273" />
    <state width="1493" height="69" key="GridCell.Tab.2.bottom" timestamp="1758691860273">
      <screen x="0" y="0" width="1536" height="816" />
    </state>
    <state width="1493" height="69" key="GridCell.Tab.2.bottom/0.0.1536.816/-312.-1440.2560.1392@0.0.1536.816" timestamp="1758691860273" />
    <state width="1493" height="69" key="GridCell.Tab.2.center" timestamp="1758691860273">
      <screen x="0" y="0" width="1536" height="816" />
    </state>
    <state width="1493" height="69" key="GridCell.Tab.2.center/0.0.1536.816/-312.-1440.2560.1392@0.0.1536.816" timestamp="1758691860273" />
    <state width="1493" height="69" key="GridCell.Tab.2.left" timestamp="1758691860273">
      <screen x="0" y="0" width="1536" height="816" />
    </state>
    <state width="1493" height="69" key="GridCell.Tab.2.left/0.0.1536.816/-312.-1440.2560.1392@0.0.1536.816" timestamp="1758691860273" />
    <state width="1493" height="69" key="GridCell.Tab.2.right" timestamp="1758691860273">
      <screen x="0" y="0" width="1536" height="816" />
    </state>
    <state width="1493" height="69" key="GridCell.Tab.2.right/0.0.1536.816/-312.-1440.2560.1392@0.0.1536.816" timestamp="1758691860273" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/service/MyService.java</url>
          <line>31</line>
          <option name="timeStamp" value="7" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
</project>