package com.config;


import org.flowable.spring.ProcessEngineFactoryBean;
import org.flowable.spring.SpringProcessEngineConfiguration;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.PlatformTransactionManager;

import javax.sql.DataSource;

/**
 * 多数据源配置类
 * 主数据源：业务数据库 (business_db)
 * Flowable数据源：工作流数据库 (flowable_demo)
 */
@Configuration
public class FlowableDataSourceConfig {

    /**
     * 主数据源（业务数据库）
     */
    @Bean(name = "primaryDataSource")
    @Primary
    @ConfigurationProperties("spring.datasource.primary")
    public DataSource primaryDataSource() {
        return DataSourceBuilder.create().build();
    }

    /**
     * Flowable数据源（工作流数据库）
     */
    @Bean(name = "flowableDataSource")
    @ConfigurationProperties("spring.datasource.flowable")
    public DataSource flowableDataSource() {
        return DataSourceBuilder.create().build();
    }

    /**
     * 主数据源事务管理器
     */
    @Bean(name = "primaryTransactionManager")
    @Primary
    public PlatformTransactionManager primaryTransactionManager(@Qualifier("primaryDataSource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

    /**
     * Flowable事务管理器
     */
    @Bean(name = "flowableTransactionManager")
    public PlatformTransactionManager flowableTransactionManager(@Qualifier("flowableDataSource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

    /**
     * Flowable流程引擎配置
     */
    @Bean
    public SpringProcessEngineConfiguration flowableProcessEngineConfiguration(
            @Qualifier("flowableDataSource") DataSource dataSource,
            @Qualifier("flowableTransactionManager") PlatformTransactionManager transactionManager) {
        SpringProcessEngineConfiguration config = new SpringProcessEngineConfiguration();
        config.setDataSource(dataSource); // 指定Flowable专用数据源
        config.setTransactionManager(transactionManager);
        config.setDatabaseSchemaUpdate("create-drop"); // 强制重新创建表结构
        config.setAsyncExecutorActivate(false); // 关闭异步执行器
        // 使用默认历史记录级别
        return config;
    }

    /**
     * Flowable流程引擎
     */
    @Bean
    public ProcessEngineFactoryBean processEngine(SpringProcessEngineConfiguration config) {
        ProcessEngineFactoryBean factoryBean = new ProcessEngineFactoryBean();
        factoryBean.setProcessEngineConfiguration(config);
        return factoryBean;
    }
}