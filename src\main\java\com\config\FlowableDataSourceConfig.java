package com.config;

import org.flowable.spring.ProcessEngineFactoryBean;
import org.flowable.spring.SpringProcessEngineConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.PlatformTransactionManager;

import javax.sql.DataSource;

@Configuration
public class FlowableDataSourceConfig {

    @Bean
    @ConfigurationProperties("spring.flowable")
    public DataSource flowableDataSource() {
        return DataSourceBuilder.create().build();
    }

    @Bean
    public PlatformTransactionManager flowableTransactionManager() {
        return new DataSourceTransactionManager(flowableDataSource());
    }

    @Bean
    public SpringProcessEngineConfiguration flowableProcessEngineConfiguration() {
        SpringProcessEngineConfiguration config = new SpringProcessEngineConfiguration();
        config.setDataSource(flowableDataSource()); // 👈 指定数据源
        config.setTransactionManager(flowableTransactionManager());
        config.setDatabaseSchemaUpdate("true"); // 自动建表
        config.setAsyncExecutorActivate(false); // 关闭异步执行器（按需）
        return config;
    }

    @Bean
    public ProcessEngineFactoryBean processEngine() {
        ProcessEngineFactoryBean factoryBean = new ProcessEngineFactoryBean();
        factoryBean.setProcessEngineConfiguration(flowableProcessEngineConfiguration());
        return factoryBean;
    }
}