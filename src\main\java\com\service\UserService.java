package com.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import javax.sql.DataSource;
import java.util.List;
import java.util.Map;

/**
 * 用户业务服务类
 * 使用主数据源（business_db）
 */
@Service
public class UserService {

    private final JdbcTemplate primaryJdbcTemplate;

    @Autowired
    public UserService(@Qualifier("primaryDataSource") DataSource primaryDataSource) {
        this.primaryJdbcTemplate = new JdbcTemplate(primaryDataSource);
    }

    /**
     * 获取所有用户信息
     */
    public List<Map<String, Object>> getAllUsers() {
        String sql = "SELECT * FROM user_info ORDER BY id";
        return primaryJdbcTemplate.queryForList(sql);
    }

    /**
     * 根据用户名获取用户信息
     */
    public Map<String, Object> getUserByUsername(String username) {
        String sql = "SELECT * FROM user_info WHERE username = ?";
        List<Map<String, Object>> users = primaryJdbcTemplate.queryForList(sql, username);
        return users.isEmpty() ? null : users.get(0);
    }

    /**
     * 创建新用户
     */
    public int createUser(String username, String email, String phone) {
        String sql = "INSERT INTO user_info (username, email, phone) VALUES (?, ?, ?)";
        return primaryJdbcTemplate.update(sql, username, email, phone);
    }

    /**
     * 测试主数据源连接
     */
    public String testPrimaryDataSource() {
        try {
            String sql = "SELECT COUNT(*) as count FROM user_info";
            Map<String, Object> result = primaryJdbcTemplate.queryForMap(sql);
            return "主数据源连接成功！用户表中有 " + result.get("count") + " 条记录";
        } catch (Exception e) {
            return "主数据源连接失败：" + e.getMessage();
        }
    }
}
